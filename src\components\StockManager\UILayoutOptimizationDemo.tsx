import React from 'react';
import { CheckCircle, Clock, AlertCircle } from 'lucide-react';

/**
 * UI布局优化演示组件
 * 展示股票资金流向监控界面的布局优化效果
 */
export const UILayoutOptimizationDemo: React.FC = () => {
  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* 页面标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          股票资金流向监控界面 UI 布局优化
        </h1>
        <p className="text-lg text-gray-600">
          优化状态显示区域布局，添加更新时间显示
        </p>
      </div>

      {/* 优化前后对比 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 优化前 */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 text-center">
            优化前 ❌
          </h2>
          
          {/* 模拟原来的布局 */}
          <div className="space-y-4">
            {/* 股票代码操作行 */}
            <div className="flex items-center justify-between text-sm text-gray-600 pb-2 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <span>股票代码 (5)</span>
              </div>
              <div className="flex items-center gap-2">
                <span>操作</span>
              </div>
            </div>

            {/* 独占一行的绿色状态区域 */}
            <div className="rounded-lg border p-3 bg-green-50 border-green-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-sm font-medium text-green-500">
                    所有股票数据加载成功
                  </span>
                </div>
              </div>
              <div className="mt-2 space-y-1">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="h-2 rounded-full bg-green-500" style={{ width: '100%' }} />
                </div>
                <div className="flex justify-between text-xs text-gray-600">
                  <span>总计: 5</span>
                  <span>成功: 5</span>
                  <span>成功率: 100.0%</span>
                </div>
              </div>
            </div>

            {/* 缺少更新时间显示 */}
            <div className="text-center text-sm text-gray-500">
              ❌ 没有更新时间显示
            </div>
          </div>
        </div>

        {/* 优化后 */}
        <div className="bg-white rounded-lg border p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4 text-center">
            优化后 ✅
          </h2>
          
          {/* 模拟新的布局 */}
          <div className="space-y-4">
            {/* 单行布局：股票代码 + 状态 + 更新时间 + 操作 */}
            <div className="flex items-center justify-between text-sm text-gray-600 pb-2 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <span>股票代码 (5)</span>
              </div>

              {/* 中间区域：紧凑的状态显示和更新时间 */}
              <div className="flex items-center gap-4 flex-1 justify-center">
                {/* 更新状态指示器 */}
                <div className="flex items-center gap-1">
                  <CheckCircle className="w-3 h-3 text-green-500" />
                  <span className="text-xs text-gray-600">5/5</span>
                </div>

                {/* 更新时间显示 */}
                <div className="flex items-center gap-1 px-2 py-1 bg-gray-50 rounded text-xs text-gray-600">
                  <Clock className="w-3 h-3" />
                  <span>更新时间: 2025-01-02 14:30:25</span>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <span>操作</span>
              </div>
            </div>

            {/* 详细状态仅在股票较多时显示 */}
            <div className="text-center text-sm text-gray-500">
              ✅ 详细状态仅在股票数量 > 20 时显示
            </div>
          </div>
        </div>
      </div>

      {/* 优化说明 */}
      <div className="bg-blue-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 mb-4">优化说明</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-medium text-blue-800 mb-2">1. 状态显示区域重新布局</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 将绿色状态区域从独占一行改为与股票代码操作同一行</li>
              <li>• 缩小状态显示区域宽度，使用紧凑的图标+数字显示</li>
              <li>• 状态信息放置在中间空白位置，形成紧凑的单行布局</li>
              <li>• 详细状态信息仅在股票数量超过20个时显示</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-blue-800 mb-2">2. 添加更新时间显示</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• 在状态信息旁边添加更新时间显示</li>
              <li>• 使用时钟图标和标签形式展示</li>
              <li>• 格式为 "更新时间: YYYY-MM-DD HH:mm:ss"</li>
              <li>• 显示实际的数据更新时间戳</li>
            </ul>
          </div>
        </div>
      </div>

      {/* 技术实现 */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">技术实现</h3>
        <div className="space-y-4">
          <div>
            <h4 className="font-medium text-gray-800 mb-2">修改的文件</h4>
            <code className="text-sm bg-white px-2 py-1 rounded border">
              src/components/StockManager/StockList.tsx
            </code>
          </div>
          
          <div>
            <h4 className="font-medium text-gray-800 mb-2">主要变更</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• 重新设计列表头部布局，使用 flex 布局实现单行显示</li>
              <li>• 添加中间区域用于显示紧凑的状态信息和更新时间</li>
              <li>• 使用 React Query 的 dataUpdatedAt 获取实际更新时间</li>
              <li>• 条件渲染详细状态组件，仅在股票数量较多时显示</li>
            </ul>
          </div>

          <div>
            <h4 className="font-medium text-gray-800 mb-2">用户体验改进</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• 界面更加紧凑，信息层次更清晰</li>
              <li>• 用户可以清楚地看到数据的更新时间</li>
              <li>• 减少了界面的垂直空间占用</li>
              <li>• 保持了所有原有功能的完整性</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UILayoutOptimizationDemo;
